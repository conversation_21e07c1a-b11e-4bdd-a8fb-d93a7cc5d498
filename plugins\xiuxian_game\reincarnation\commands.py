"""
冥返系统命令模块
"""
from nonebot import on_command
from nonebot.adapters.qq import MessageEvent
from ..models.db import safe_session
from ..models.player import Player
from ..utils import message_add_head
from .service import ReincarnationService


reincarnate_cmd = on_command("冥返", aliases={"轮回", "重生"}, block=True, priority=5)


@reincarnate_cmd.handle()
async def handle_reincarnate(event: MessageEvent):
    """处理冥返命令"""
    user_id = event.get_user_id()
    
    async with safe_session() as session:
        player = await session.get(Player, user_id)
        if not player:
            await reincarnate_cmd.finish("⛔ 请先使用【注册】命令创建角色")
        
        # 检查是否可以冥返
        if not await ReincarnationService.can_reincarnate(player):
            from ..config import config
            max_level = config.game_config["game"]["max_level"]
            await reincarnate_cmd.finish(
                f"⛔ 冥返条件不足！\n"
                f"━━━━━━━━━━━━━\n"
                f"✧ 当前等级：Lv.{player.level}\n"
                f"✧ 需要等级：Lv.{max_level}\n"
                f"━━━━━━━━━━━━━\n"
                "⬇️ 【修炼】【突破】提升等级"
            )
        
        # 执行冥返
        result_msg = await ReincarnationService.perform_reincarnation(session, player)
        await reincarnate_cmd.finish(message_add_head(result_msg, event))
